import boto3
import json

# Create a Bedrock client
client = boto3.client('bedrock-runtime', region_name='us-west-2')

# AWS Nova Lite model ID
model_id = 'openai.gpt-oss-20b-1:0'

# System prompt (optional, can be customized)
system_prompt = '''
You are very helpful assistant which extracts data in key-value pair from given document. To achieve that follow below steps

Step 1:
Read the data given to you in user prompt in following structure:
=== NON-TABLE TEXT ===
<non-table-text>

=== TABLE 1 ===
<table-1-data>
=== TABLE 2 ===
<table-2-data>
...
=== TABLE n ===
<table-n-data>

Step 2:
Refer to image for location of given data and it's context

Step 3:
Extract complete data in key-value pair from given data. 

Step 4:
Once again make sure that each and every item is covered in extracted key-value pairs, If not then include it in key-value pair.

Step 5:
Use tool call to with extracted data
'''

with open('structured_text.json', 'r') as f:
    structured_text = json.load(f)

# Prepare the conversation messages
messages = [
    {
        "role": "user",
        "content": [
            {"text": structured_text},
        ]
    }
]

# Call the model using converse API
response = client.converse(
    modelId=model_id,
    messages=messages,
    system=[{"text": system_prompt}],
    inferenceConfig={
        "maxTokens": 250,
        "temperature": 0.2
    }
)

# Print the response
print("Response:")

# Extract the text response from the content array
content_items = response['output']['message']['content']

# Find the item with 'text' key (skip reasoning content)
for item in content_items:
    if 'text' in item:
        response_text = item['text']
        # Remove reasoning tags if present
        if '<reasoning>' in response_text and '</reasoning>' in response_text:
            # Extract text after the closing reasoning tag
            response_text = response_text.split('</reasoning>')[1].strip()
        print(response_text)
        break

print(response)