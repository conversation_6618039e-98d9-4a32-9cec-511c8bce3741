import boto3

# Create a Bedrock client
client = boto3.client('bedrock-runtime', region_name='us-west-2')

# AWS Nova Lite model ID
model_id = 'openai.gpt-oss-20b-1:0'

# System prompt (optional, can be customized)
system_prompt = "You are <PERSON> Lite, a helpful AI assistant."

# Prepare the conversation messages
messages = [
    {
        "role": "user",
        "content": [
            {"text": "Hello, Nova Lite! Can you tell me a joke?"}
        ]
    }
]

# Call the model using converse API
response = client.converse(
    modelId=model_id,
    messages=messages,
    system=[{"text": system_prompt}],
    inferenceConfig={
        "maxTokens": 250,
        "temperature": 0.2
    }
)

# Print the response
print("Response from Nova Lite:")

# Extract the text response from the content array
content_items = response['output']['message']['content']

# Find the item with 'text' key (skip reasoning content)
for item in content_items:
    if 'text' in item:
        response_text = item['text']
        # Remove reasoning tags if present
        if '<reasoning>' in response_text and '</reasoning>' in response_text:
            # Extract text after the closing reasoning tag
            response_text = response_text.split('</reasoning>')[1].strip()
        print(response_text)
        break
